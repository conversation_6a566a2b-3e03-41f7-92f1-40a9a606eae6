import React, { useEffect, useState } from 'react';
import Modal, { ModalSize } from '../../../app/shared/Modal.tsx';
import CustomForm from '../../../app/shared/form/Form.tsx';
import { Col, Form, Row, Select, Input } from 'antd';
import Button, { ButtonVariation } from '../../../app/shared/Button.tsx';
import ClaimStatusTag from '../../../app/shared/ClaimStatusTag.tsx';
import {
    Estimate,
    EstimateCreateProduct,
    EstimateItem,
    PriceBookItem,
} from '../../../../store/claims/types/Claim.ts';
import { ReactComponent as IconPlus } from '../../../../assets/icons/icon-plus.svg';
import { ReactComponent as IconMinusFilled } from '../../../../assets/icons/icon-minus-rounded-fill.svg';
import SelectField from '../../../app/shared/form/SelectField.tsx';
import { useClaimStore, useFileStore } from '../../../../store';
import { useClaimsContext } from '../../ClaimsWrapper.tsx';
import FlashMessages from '../../../app/shared/FlashMessages.tsx';
import { formatCurrency, formatNumberOutput } from '../../../../utils/helpers.ts';
import { PriceBookSortingGroups } from './utils.ts';
import classNames from 'classnames';
import InputField from '../../../app/shared/form/InputField.tsx';
import Loader from '../../../app/shared/Loader.tsx';
import { RuleObject } from 'antd/es/form';
import Dialog from '../../../app/shared/Dialog.tsx';
import EstimateSummaryModal from './EstimateSummaryModal.tsx';
import EstimateConfirmationModal from './EstimateConfirmationModal.tsx';

interface Props {
    onChangeFile?: VoidFunction;
    onClose: VoidFunction;
    onSuccess: (estimate: Estimate) => void;
    show: boolean;
    uploadedFile: File | string;
    estimate?: Estimate;
}

const CreateEstimateModal: React.FC<Props> = ({
    estimate,
    onChangeFile,
    show,
    onClose,
    onSuccess,
    uploadedFile,
}) => {
    const [form] = Form.useForm();
    const formValues = Form.useWatch([], form);
    const { getClaimPriceBook, setEstimate, getClaimEstimateDocumentLink } = useClaimStore();
    const { uploadFileS3 } = useFileStore();
    const { currentClaim } = useClaimsContext();
    const [groupedProducts, setGroupedProducts] = useState<Record<string, PriceBookItem[]>>({});
    const [canSubmit, setCanSubmit] = useState<boolean>(true);
    const [infoExpanded, setInfoExpanded] = useState<boolean>(false);
    const [showConfirmModal, setShowConfirmModal] = useState<boolean>(false);
    const [priceBookLoading, setPriceBookLoading] = useState<boolean>(false);
    const [estimateItems, setEstimateItems] = useState<Record<string, EstimateItem[]>>({});
    const [groupSelectEnabled, setGroupSelectEnabled] = useState<Record<string, boolean>>({});
    const [selectedTab, setSelectedTab] = useState<string>('');
    const [showEstimateSummary, setShowEstimateSummary] = useState<boolean>(false);
    const [showConfirmationModal, setShowConfirmationModal] = useState<boolean>(false);
    const [currentEstimateItems, setCurrentEstimateItems] = useState<EstimateItem[]>([]);
    const [tempFormValues, setTempFormValues] = useState<Record<string, any>>({});

    const groupNames = Object.keys(groupedProducts);

    useEffect(() => {
        if (!estimate?.items) {
            return;
        }
        setEstimateItems(
            estimate.items.reduce<Record<string, EstimateItem[]>>((acc, product) => {
                acc[product.product_family] = acc[product.product_family] || [];
                acc[product.product_family].push(product);
                return acc;
            }, {}),
        );
    }, []);

    const onSubmit = async (fields: any) => {
        if (!currentClaim?.id) {
            return;
        }
        setCanSubmit(false);
        try {
            let documentLink = estimate?.original_estimate || '';
            if (typeof uploadedFile === 'object') {
                const name = uploadedFile.name;
                const documentLinkObject = await getClaimEstimateDocumentLink(
                    currentClaim?.id,
                    name,
                );
                await uploadFileS3(uploadedFile, documentLinkObject.presigned_url);
                documentLink = documentLinkObject.url;
            }
            const products: EstimateCreateProduct[] = Object.values(
                fields,
            ).flat() as EstimateCreateProduct[];

            const createdEstimate = await setEstimate(currentClaim?.id, {
                original_estimate: documentLink,
                line_item_description: currentClaim?.project_description || undefined,
                products: products,
            });
            onSuccess(createdEstimate);
        } catch (errors) {
            FlashMessages.error('Something went wrong, please try again!');
            console.error(errors);
        } finally {
            setCanSubmit(true);
        }
    };

    const handleEstimateSummary = () => {
        // Convert form values from ALL tabs to EstimateItem format for summary
        const allFormValues = form.getFieldsValue();

        console.log('=== ESTIMATE SUMMARY DEBUG ===');
        console.log('Getting estimate summary for all tabs');
        console.log('Method 1 - form.getFieldsValue():', JSON.stringify(allFormValues, null, 2));
        console.log('Method 2 - formValues (useWatch):', JSON.stringify(formValues, null, 2));

        // Try getting values for each group individually
        console.log('\n=== INDIVIDUAL GROUP VALUES ===');
        const individualGroupValues: Record<string, any> = {};
        groupNames.forEach(groupName => {
            const groupValue = form.getFieldValue(groupName);
            individualGroupValues[groupName] = groupValue;
            console.log(`${groupName}:`, groupValue);
        });
        console.log('Combined individual values:', JSON.stringify(individualGroupValues, null, 2));

        console.log('\n=== COMPARISON ===');
        console.log('Available group names:', groupNames);
        console.log('Current selectedTab:', selectedTab);
        console.log('allFormValues keys:', Object.keys(allFormValues));
        console.log('individualGroupValues keys:', Object.keys(individualGroupValues));
        console.log('formValues keys:', Object.keys(formValues || {}));

        // Use the individual group values if they contain more data
        const dataToUse = Object.keys(individualGroupValues).length > Object.keys(allFormValues).length
            ? individualGroupValues
            : allFormValues;

        console.log('\n=== USING DATA SOURCE ===');
        console.log('Selected data source:', Object.keys(individualGroupValues).length > Object.keys(allFormValues).length ? 'individualGroupValues' : 'allFormValues');
        console.log('Data to use:', JSON.stringify(dataToUse, null, 2));

        // Use the same approach as onSubmit: Object.values(fields).flat()
        const allProducts = Object.values(dataToUse).flat() as any[];
        console.log('All products (flattened):', allProducts);

        // Convert to EstimateItem format
        const items: EstimateItem[] = [];

        // Process each category separately to maintain product_family information
        Object.entries(dataToUse).forEach(([category, categoryItems]) => {
            console.log(`\n--- Processing category: ${category} ---`);
            console.log(`Category items:`, categoryItems);
            console.log(`Is array:`, Array.isArray(categoryItems));
            console.log(`Array length:`, Array.isArray(categoryItems) ? categoryItems.length : 'N/A');

            if (Array.isArray(categoryItems)) {
                categoryItems.forEach((item: any, index: number) => {
                    console.log(`\n  Processing item ${index} in ${category}:`);
                    console.log(`    Raw item:`, JSON.stringify(item, null, 4));
                    console.log(`    Has product_id:`, !!item.product_id);
                    console.log(`    Has name:`, !!item.name);
                    console.log(`    Condition check (product_id || name):`, !!(item && (item.product_id || item.name)));

                    if (item && (item.product_id || item.name)) { // Include items that have been properly added
                        const estimateItem: any = {
                            product_id: item.product_id || '',
                            quantity: Number(item.quantity) || 0,
                            unit_price: Number(item.unit_price) || 0,
                            product_name: item.name || '',
                            product_description: '',
                            product_family: category,
                        };

                        // Add length field for Storage items
                        if (category === 'Storage' && item.length) {
                            estimateItem.length = Number(item.length);
                        }

                        console.log(`    ✅ ADDING estimate item:`, JSON.stringify(estimateItem, null, 4));
                        items.push(estimateItem);
                    } else {
                        console.log(`    ❌ SKIPPING item (incomplete):`, item);
                    }
                });
            } else {
                console.log(`  ❌ Category ${category} is not an array or is empty`);
            }
            console.log(`--- End processing ${category} ---\n`);
        });

        console.log('Final estimate items for summary:', items);
        console.log('Total items found:', items.length);
        console.log('=== END ESTIMATE SUMMARY DEBUG ===');

        setCurrentEstimateItems(items);
        setShowEstimateSummary(true);
    };

    const handleConfirmationYes = () => {
        setShowConfirmationModal(false);
        handleEstimateSummary();
    };

    const handleConfirmationNo = () => {
        setShowConfirmationModal(false);
    };

    useEffect(() => {
        if (!currentClaim?.id) {
            return;
        }
        setPriceBookLoading(true);
        getClaimPriceBook(currentClaim.id)
            .then((r) => {
                const grouped = r.reduce<Record<string, PriceBookItem[]>>((acc, product) => {
                    acc[product.product_family] = acc[product.product_family] || [];
                    acc[product.product_family].push(product);
                    return acc;
                }, {});

                const remainingKeys = Object.keys(grouped).filter(
                    (key) => !PriceBookSortingGroups.includes(key),
                );

                const sortedGrouped = [...PriceBookSortingGroups, ...remainingKeys].reduce<
                    Record<string, PriceBookItem[]>
                >((acc, key) => {
                    if (grouped[key]) {
                        acc[key] = grouped[key];
                    }
                    return acc;
                }, {});

                setGroupedProducts(sortedGrouped);
            })
            .finally(() => {
                setPriceBookLoading(false);
            });
    }, [currentClaim?.id]);

    useEffect(() => {
        if (!selectedTab && Object.keys(groupedProducts).length > 0) {
            const firstTab = Object.keys(groupedProducts)[0];
            console.log('Setting initial selectedTab to:', firstTab);
            setSelectedTab(firstTab);
        }
    }, [groupedProducts]);

    // Debug form values
    useEffect(() => {
        console.log('Form values changed:', formValues);
        console.log('Group select enabled:', groupSelectEnabled);
        console.log('Temp form values:', tempFormValues);
        console.log('Selected tab:', selectedTab);
    }, [formValues, groupSelectEnabled, tempFormValues, selectedTab]);

    const handleGroupSelectEnabled = (groupSelect: string, value: boolean) => {
        setGroupSelectEnabled((prevState) => ({
            ...prevState,
            [groupSelect]: value,
        }));
    };

    const generateAccordionItems = (groupedProducts: Record<string, PriceBookItem[]>) => {
        return Object.keys(groupedProducts).map((groupName, index) => {
            const selectOptions = groupedProducts[groupName].map((product) => {
                const estimateProductItem = estimateItems[groupName]?.find(
                    (item) => item.product_id === product.product_id,
                );

                return {
                    pricebook_entry_id: product.id,
                    label: product.product_name,
                    name: product.product_name,
                    value: product.id,
                    unit_price: estimateProductItem?.unit_price
                        ? formatNumberOutput(estimateProductItem.unit_price)
                        : undefined,
                    product_id: product.product_id,
                    quantity: estimateProductItem?.quantity ? estimateProductItem.quantity : 1,
                };
            });

            const initialValue =
                (!!Object.keys(estimateItems).length &&
                    estimateItems[groupName] &&
                    selectOptions.filter((item) =>
                        estimateItems[groupName].find(
                            (estimateItem) => estimateItem.product_id === item.product_id,
                        ),
                    )) ||
                [];

            return {
                className: 'claim-ce-form-boxes-item',
                headerClass: 'claim-ce-form-boxes-item-top',
                key: index.toString(),
                label: (
                    <>
                        {groupName}{' '}
                        {!!formValues[groupName]?.length && (
                            <span className="tag">{formValues[groupName]?.length} added</span>
                        )}
                    </>
                ),
                children: (
                    <>
                        <Form.List name={groupName} initialValue={initialValue}>
                            {(fields, { add, remove }) => {
                                return (
                                    <>
                                        <div className="claim-ce-form-boxes-item-head">
                                            <SelectField
                                                className="select"
                                                placeholder="Select a service"
                                                name={`${groupName}-select`}
                                                options={selectOptions}
                                                onOptionChange={() =>
                                                    handleGroupSelectEnabled(
                                                        `${groupName}-select`,
                                                        true,
                                                    )
                                                }
                                            />
                                            <Form.Item>
                                                <Button
                                                    color="primary"
                                                    variant="solid"
                                                    htmlType="button"
                                                    onClick={() => {
                                                        const selectValue =
                                                            form.getFieldValue(groupName)[
                                                                `${groupName}-select`
                                                            ];
                                                        const item = selectOptions.find(
                                                            (item) => item.value === selectValue,
                                                        );
                                                        if (item) {
                                                            add({
                                                                name: item.label,
                                                                pricebook_entry_id:
                                                                    item.pricebook_entry_id,
                                                                product_id: item.product_id,
                                                                quantity: 1,
                                                                unit_price: undefined,
                                                            });
                                                            handleGroupSelectEnabled(
                                                                `${groupName}-select`,
                                                                false,
                                                            );
                                                        }
                                                    }}
                                                    size="large"
                                                    className="margin-right-16"
                                                    wide={true}
                                                    disabled={
                                                        !groupSelectEnabled[`${groupName}-select`]
                                                    }
                                                    icon={<IconPlus className="icon-regular" />}
                                                >
                                                    Add Service
                                                </Button>
                                            </Form.Item>
                                        </div>
                                        <div className="claim-ce-form-boxes-item-list">
                                            <Row className="claim-ce-form-boxes-item-list-head">
                                                <Col span={8}>Service title</Col>
                                                <Col span={4}>Qty</Col>
                                                <Col span={5}>Price</Col>
                                                <Col span={5} className="end">
                                                    Total
                                                </Col>
                                                <Col span={2}></Col>
                                            </Row>
                                            <div className="claim-ce-form-boxes-item-list-body">
                                                {fields &&
                                                    fields.map((field, index) => {
                                                        const selectedServiceName =
                                                            form.getFieldValue([
                                                                groupName,
                                                                field.name,
                                                                'name',
                                                            ]);
                                                        const selectedServicePrice =
                                                            form.getFieldValue([
                                                                groupName,
                                                                field.name,
                                                                'unit_price',
                                                            ]);
                                                        const selectedServiceQuantity =
                                                            form.getFieldValue([
                                                                groupName,
                                                                field.name,
                                                                'quantity',
                                                            ]);

                                                        return (
                                                            <Row key={index}>
                                                                <Col span={8}>
                                                                    <Form.Item
                                                                        name={[field.name, 'name']}
                                                                        noStyle
                                                                    >
                                                                        <span>
                                                                            {selectedServiceName ||
                                                                                'No name selected'}
                                                                        </span>
                                                                    </Form.Item>
                                                                </Col>
                                                                <Col span={4}>
                                                                    <InputField
                                                                        type="number"
                                                                        inputProps={{
                                                                            onWheel: (e) =>
                                                                                e.currentTarget.blur(),
                                                                        }}
                                                                        className="input-wrapper"
                                                                        name={[
                                                                            field.name,
                                                                            'quantity',
                                                                        ]}
                                                                        rules={[
                                                                            {
                                                                                required: true,
                                                                                message: '',
                                                                            },
                                                                        ]}
                                                                    />
                                                                </Col>
                                                                <Col span={5}>
                                                                    <InputField
                                                                        type="number"
                                                                        inputProps={{
                                                                            onWheel: (e) =>
                                                                                e.currentTarget.blur(),
                                                                        }}
                                                                        className="input-wrapper"
                                                                        name={[
                                                                            field.name,
                                                                            'unit_price',
                                                                        ]}
                                                                        placeholder="0.00"
                                                                        rules={[
                                                                            {
                                                                                required: true,
                                                                                message: '',
                                                                            },
                                                                            {
                                                                                validator: (
                                                                                    _: RuleObject,
                                                                                    value:
                                                                                        | string
                                                                                        | undefined,
                                                                                ) => {
                                                                                    if (
                                                                                        value ===
                                                                                            undefined ||
                                                                                        value ===
                                                                                            null ||
                                                                                        value === ''
                                                                                    ) {
                                                                                        return Promise.reject(
                                                                                            '',
                                                                                        );
                                                                                    }
                                                                                    if (
                                                                                        isNaN(
                                                                                            Number(
                                                                                                value,
                                                                                            ),
                                                                                        )
                                                                                    ) {
                                                                                        return Promise.reject(
                                                                                            '',
                                                                                        );
                                                                                    }
                                                                                    return Promise.resolve();
                                                                                },
                                                                            },
                                                                        ]}
                                                                    />
                                                                </Col>
                                                                <Col span={5} className="end">
                                                                    <Form.Item>
                                                                        <span className="claim-ce-form-boxes-item-list-body-total">
                                                                            {formatCurrency(
                                                                                selectedServiceQuantity *
                                                                                    selectedServicePrice ||
                                                                                    0,
                                                                            )}
                                                                        </span>
                                                                    </Form.Item>
                                                                </Col>
                                                                <Col span={2} className="end">
                                                                    <IconMinusFilled
                                                                        className="icon-regular cursor"
                                                                        onClick={() =>
                                                                            remove(field.name)
                                                                        }
                                                                    />
                                                                </Col>
                                                            </Row>
                                                        );
                                                    })}
                                            </div>
                                        </div>
                                    </>
                                );
                            }}
                        </Form.List>
                    </>
                ),
            };
        });
    };

    const accordionItems = generateAccordionItems(groupedProducts);

    return (
        <Modal
            className="claim-ce"
            onClose={() => setShowConfirmModal(true)}
            show={show}
            size={ModalSize.LARGE}
            title="Add Services"
            sideContent={{
                content: (
                    <div>
                        <div className="claim-header">
                            <h1 className="heading h3">{currentClaim?.claim_name}</h1>
                            <ClaimStatusTag status={currentClaim?.status} />
                        </div>

                        <div className="claim-ce-content">
                            <div className="claim-ce-info">
                                <div
                                    className={classNames('claim-ce-info-inner', {
                                        expanded: infoExpanded,
                                    })}
                                >
                                    <h5 className="claim-ce-info-title">Create your estimate</h5>
                                    <div className="claim-ce-info-group">
                                        <p className="claim-ce-info-group-description">
                                            Now it’s time to input your line items. Using our
                                            service page, select a service and enter the
                                            corresponding rate and quantity.
                                        </p>
                                    </div>
                                    <div className="claim-ce-info-group">
                                        <h6 className="claim-ce-info-group-title">Helpful Tips!</h6>
                                        <p className="claim-ce-info-group-description">
                                            For materials, group them under one service and input
                                            the total cost for all items from your original
                                            estimate. This will streamline the process and ensure
                                            accuracy. Pack Out = Move Out. Pack Back = Move Back.
                                        </p>
                                    </div>
                                    <div className="claim-ce-info-group">
                                        <p className="claim-ce-info-group-description">
                                            <b>Storage:</b> Select the type of storage required
                                            (e.g., vault or lb) and enter the estimated storage
                                            needed in Qty. Storage is estimated for one month.
                                        </p>
                                    </div>
                                </div>
                                <div
                                    className="claim-ce-info-action"
                                    onClick={() => setInfoExpanded(!infoExpanded)}
                                >
                                    {infoExpanded ? 'Less' : 'Learn More'}
                                </div>
                            </div>

                            <div className="claim-ce-viewer-head">
                                <h6 className="claim-ce-viewer-head-title">Original estimate</h6>

                                <div className="claim-ce-viewer-head-actions">
                                    <Button
                                        color="primary"
                                        variant="link"
                                        variation={ButtonVariation.LINK}
                                        onClick={onChangeFile}
                                    >
                                        Change
                                    </Button>
                                </div>
                            </div>
                            {/* {<FileViewer file={uploadedFile} />} */}
                        </div>
                    </div>
                ),
            }}
        >
            {!canSubmit && <Loader className="claim-ce-loader" />}
            <CustomForm form={form} onSubmit={onSubmit} className="claim-ce-form">
                {priceBookLoading && <Loader />}

                {!priceBookLoading && groupNames.length > 0 && (
                    <div className="claim-ce-tab-layout">
                        <div className="claim-ce-tabs">
                            {groupNames.map((group: string) => (
                                <div
                                    key={group}
                                    className={`claim-ce-tab${selectedTab === group ? ' selected' : ''}`}
                                    onClick={() => {
                                        console.log('Switching to tab:', group);
                                        setSelectedTab(group);
                                    }}
                                >
                                    {group}
                                </div>
                            ))}
                        </div>
                        <div className="claim-ce-tab-content">
                            {/* Render the table and form for the selected tab only */}
                            {(() => {
                                const groupName = selectedTab;
                                const selectOptions =
                                    groupedProducts[groupName]?.map((product) => {
                                        const estimateProductItem = estimateItems[groupName]?.find(
                                            (item) => item.product_id === product.product_id,
                                        );
                                        return {
                                            pricebook_entry_id: product.id,
                                            label: product.product_name,
                                            name: product.product_name,
                                            value: product.id,
                                            unit_price: estimateProductItem?.unit_price
                                                ? formatNumberOutput(estimateProductItem.unit_price)
                                                : undefined,
                                            product_id: product.product_id,
                                            quantity: estimateProductItem?.quantity
                                                ? estimateProductItem.quantity
                                                : 1,
                                        };
                                    }) || [];
                                const initialValue =
                                    (!!Object.keys(estimateItems).length &&
                                        estimateItems[groupName] &&
                                        selectOptions.filter((item) =>
                                            estimateItems[groupName].find(
                                                (estimateItem) =>
                                                    estimateItem.product_id === item.product_id,
                                            ),
                                        )) ||
                                    [];
                                return (
                                    <Form.List name={groupName} initialValue={initialValue}>
                                        {(fields, { add, remove }) => (
                                            <>
                                                <div className="claim-ce-form-boxes-item-head">
                                                    <Row gutter={16} align="bottom" style={{ width: '100%' }}>
                                                        <Col span={groupName === 'Storage' ? 6 : 8}>
                                                            <Select
                                                                className="select"
                                                                placeholder="Select a Service"
                                                                options={selectOptions}
                                                                value={tempFormValues[`${groupName}_select`]}
                                                                onChange={(value) => {
                                                                    console.log(
                                                                        'Select option changed:',
                                                                        value,
                                                                        'for group:',
                                                                        groupName
                                                                    );
                                                                    setTempFormValues(prev => {
                                                                        const newValues = {
                                                                            ...prev,
                                                                            [`${groupName}_select`]: value
                                                                        };
                                                                        console.log('Updated tempFormValues:', newValues);
                                                                        return newValues;
                                                                    });
                                                                    handleGroupSelectEnabled(
                                                                        `${groupName}-select`,
                                                                        true,
                                                                    );
                                                                }}
                                                                style={{ width: '100%' }}
                                                            />
                                                        </Col>
                                                        {groupName === 'Storage' && (
                                                            <Col span={4}>
                                                                <Input
                                                                    type="number"
                                                                    placeholder="Length (Months)"
                                                                    className="input-wrapper"
                                                                    value={tempFormValues[`${groupName}_length`] || ''}
                                                                    onChange={(e) => {
                                                                        setTempFormValues(prev => ({
                                                                            ...prev,
                                                                            [`${groupName}_length`]: e.target.value
                                                                        }));
                                                                    }}
                                                                    style={{ width: '100%' }}
                                                                />
                                                            </Col>
                                                        )}
                                                        <Col span={4}>
                                                            <Input
                                                                type="number"
                                                                placeholder="#"
                                                                className="input-wrapper"
                                                                value={tempFormValues[`${groupName}_quantity`] || ''}
                                                                onChange={(e) => {
                                                                    console.log(
                                                                        'Quantity changed:',
                                                                        e.target.value,
                                                                        'for group:',
                                                                        groupName
                                                                    );
                                                                    setTempFormValues(prev => {
                                                                        const newValues = {
                                                                            ...prev,
                                                                            [`${groupName}_quantity`]: e.target.value
                                                                        };
                                                                        console.log('Updated tempFormValues:', newValues);
                                                                        return newValues;
                                                                    });
                                                                }}
                                                                style={{ width: '100%' }}
                                                            />
                                                        </Col>
                                                        <Col span={4}>
                                                            <Input
                                                                type="number"
                                                                placeholder="Rate"
                                                                className="input-wrapper"
                                                                value={tempFormValues[`${groupName}_rate`] || ''}
                                                                onChange={(e) => {
                                                                    setTempFormValues(prev => ({
                                                                        ...prev,
                                                                        [`${groupName}_rate`]: e.target.value
                                                                    }));
                                                                }}
                                                                style={{ width: '100%' }}
                                                            />
                                                        </Col>
                                                        <Col span={groupName === 'Storage' ? 6 : 4}>
                                                            <Form.Item style={{ margin: 0 }}>
                                                                <Button
                                                                    color="primary"
                                                                    variant="solid"
                                                                    htmlType="button"
                                                                    onClick={() => {
                                                                        console.log('ADD button clicked for group:', groupName);
                                                                        console.log('Current selectedTab:', selectedTab);
                                                                        console.log('All tempFormValues:', tempFormValues);

                                                                        const selectValue = tempFormValues[`${groupName}_select`];
                                                                        const lengthValue = groupName === 'Storage'
                                                                            ? tempFormValues[`${groupName}_length`]
                                                                            : undefined;
                                                                        const quantityValue = tempFormValues[`${groupName}_quantity`];
                                                                        const rateValue = tempFormValues[`${groupName}_rate`];

                                                                        console.log('Form values for current group:', {
                                                                            selectValue,
                                                                            lengthValue,
                                                                            quantityValue,
                                                                            rateValue,
                                                                            groupName,
                                                                        });

                                                                        const item = selectOptions.find(
                                                                            (item) =>
                                                                                item.value === selectValue,
                                                                        );

                                                                        console.log('Found item:', item);

                                                                        if (
                                                                            item &&
                                                                            quantityValue &&
                                                                            rateValue
                                                                        ) {
                                                                            const newItem = {
                                                                                name: item.label,
                                                                                pricebook_entry_id:
                                                                                    item.pricebook_entry_id,
                                                                                product_id: item.product_id,
                                                                                quantity:
                                                                                    Number(quantityValue),
                                                                                unit_price:
                                                                                    Number(rateValue),
                                                                                length: lengthValue
                                                                                    ? Number(lengthValue)
                                                                                    : undefined,
                                                                            };

                                                                            console.log(
                                                                                'Adding item to group:',
                                                                                groupName,
                                                                                'Item:',
                                                                                newItem,
                                                                            );
                                                                            console.log(
                                                                                'Current form values before add:',
                                                                                form.getFieldsValue(),
                                                                            );
                                                                            add(newItem);
                                                                            console.log(
                                                                                'Current form values after add:',
                                                                                form.getFieldsValue(),
                                                                            );
                                                                            handleGroupSelectEnabled(
                                                                                `${groupName}-select`,
                                                                                false,
                                                                            );
                                                                            // Clear temp form values
                                                                            console.log('Clearing form values for group:', groupName);
                                                                            setTempFormValues(prev => {
                                                                                const newValues = {
                                                                                    ...prev,
                                                                                    [`${groupName}_select`]: undefined,
                                                                                    [`${groupName}_length`]: undefined,
                                                                                    [`${groupName}_quantity`]: undefined,
                                                                                    [`${groupName}_rate`]: undefined,
                                                                                };
                                                                                console.log('Cleared tempFormValues:', newValues);
                                                                                return newValues;
                                                                            });
                                                                        } else {
                                                                            console.log(
                                                                                'Missing required fields:',
                                                                                {
                                                                                    hasItem: !!item,
                                                                                    hasQuantity:
                                                                                        !!quantityValue,
                                                                                    hasRate: !!rateValue,
                                                                                },
                                                                            );
                                                                        }
                                                                    }}
                                                                    size="large"
                                                                    className="margin-right-16"
                                                                    wide={true}
                                                                    disabled={(() => {
                                                                        const selectValue = tempFormValues[`${groupName}_select`];
                                                                        const quantityValue = tempFormValues[`${groupName}_quantity`];
                                                                        const rateValue = tempFormValues[`${groupName}_rate`];

                                                                        const isDisabled =
                                                                            !selectValue ||
                                                                            !quantityValue ||
                                                                            !rateValue;

                                                                        console.log(
                                                                            'Button disabled check:',
                                                                            {
                                                                                groupName,
                                                                                selectValue,
                                                                                quantityValue,
                                                                                rateValue,
                                                                                isDisabled,
                                                                                tempFormValues,
                                                                            },
                                                                        );

                                                                        return isDisabled;
                                                                    })()}
                                                                    icon={
                                                                        <IconPlus className="icon-regular" />
                                                                    }
                                                                    style={{ width: '100%' }}
                                                                >
                                                                    ADD
                                                                </Button>
                                                            </Form.Item>
                                                        </Col>
                                                    </Row>
                                                </div>
                                                <div className="claim-ce-form-boxes-item-list">
                                                    <Row className="claim-ce-form-boxes-item-list-head">
                                                        <Col span={groupName === 'Storage' ? 6 : 8}>Service Title</Col>
                                                        {groupName === 'Storage' && (
                                                            <Col span={4}>Length (Months)</Col>
                                                        )}
                                                        <Col span={4}>#</Col>
                                                        <Col span={4}>Rate</Col>
                                                        <Col span={4} className="end">
                                                            Total
                                                        </Col>
                                                        <Col span={2}></Col>
                                                    </Row>
                                                    <div className="claim-ce-form-boxes-item-list-body">
                                                        {(() => {
                                                            console.log('Fields array:', fields);
                                                            return null;
                                                        })()}
                                                        {fields &&
                                                            fields.map((field, index) => {
                                                                console.log(
                                                                    'Processing field:',
                                                                    field,
                                                                    'index:',
                                                                    index,
                                                                );
                                                                const selectedServiceName =
                                                                    form.getFieldValue([
                                                                        groupName,
                                                                        field.name,
                                                                        'name',
                                                                    ]);
                                                                const selectedServicePrice =
                                                                    form.getFieldValue([
                                                                        groupName,
                                                                        field.name,
                                                                        'unit_price',
                                                                    ]);
                                                                const selectedServiceQuantity =
                                                                    form.getFieldValue([
                                                                        groupName,
                                                                        field.name,
                                                                        'quantity',
                                                                    ]);
                                                                const selectedServiceLength =
                                                                    form.getFieldValue([
                                                                        groupName,
                                                                        field.name,
                                                                        'length',
                                                                    ]);
                                                                return (
                                                                    <Row key={index}>
                                                                        <Col span={groupName === 'Storage' ? 6 : 8}>
                                                                            <Form.Item
                                                                                name={[
                                                                                    field.name,
                                                                                    'name',
                                                                                ]}
                                                                                noStyle
                                                                            >
                                                                                <span>
                                                                                    {selectedServiceName ||
                                                                                        'No name selected'}
                                                                                </span>
                                                                            </Form.Item>
                                                                        </Col>
                                                                        {groupName === 'Storage' && (
                                                                            <Col span={4}>
                                                                                <span>
                                                                                    {selectedServiceLength || selectedServiceQuantity}
                                                                                </span>
                                                                            </Col>
                                                                        )}
                                                                        <Col span={4}>
                                                                            <span>
                                                                                {groupName === 'Storage'
                                                                                    ? selectedServiceQuantity
                                                                                    : `${selectedServiceQuantity} hours`}
                                                                            </span>
                                                                        </Col>
                                                                        <Col span={4}>
                                                                            <span>
                                                                                {formatCurrency(
                                                                                    selectedServicePrice || 0,
                                                                                )}
                                                                            </span>
                                                                        </Col>
                                                                        <Col span={4} className="end">
                                                                            <span className="claim-ce-form-boxes-item-list-body-total">
                                                                                {formatCurrency(
                                                                                    selectedServiceQuantity *
                                                                                        selectedServicePrice ||
                                                                                        0,
                                                                                )}
                                                                            </span>
                                                                        </Col>
                                                                        <Col span={2} className="end">
                                                                            <IconMinusFilled
                                                                                className="icon-regular cursor"
                                                                                onClick={() =>
                                                                                    remove(field.name)
                                                                                }
                                                                            />
                                                                        </Col>
                                                                    </Row>
                                                                );
                                                            })}
                                                    </div>
                                                </div>
                                                {/* Subtotal row */}
                                                <div className="claim-ce-form-boxes-item-subtotal-row">
                                                    <span className="claim-ce-form-boxes-item-subtotal-label">
                                                        {groupName} Subtotal
                                                    </span>
                                                    <span className="claim-ce-form-boxes-item-subtotal-value">
                                                        {formatCurrency(
                                                            (formValues &&
                                                                formValues[groupName]?.reduce?.(
                                                                    (sum: number, item: any) =>
                                                                        sum +
                                                                        Number(item.unit_price) *
                                                                            Number(item.quantity),
                                                                    0,
                                                                )) ||
                                                                0,
                                                        )}
                                                    </span>
                                                </div>
                                            </>
                                        )}
                                    </Form.List>
                                );
                            })()}
                        </div>
                    </div>
                )}

                {!priceBookLoading && !accordionItems.length && (
                    <p>
                        We’re updating the list of available services and it’ll be ready soon! If
                        you need any help in the meantime, don’t hesitate to reach out to us.
                    </p>
                )}
            </CustomForm>

            {/* ESTIMATE SUMMARY Button */}
            <div className="estimate-summary-button-container">
                <Button
                    color="primary"
                    variant="solid"
                    onClick={() => setShowConfirmationModal(true)}
                    className="estimate-summary-button"
                    size="large"
                    wide={true}
                >
                    ESTIMATE SUMMARY
                </Button>
            </div>

            {showConfirmModal && (
                <Dialog
                    show={showConfirmModal}
                    onClose={() => setShowConfirmModal(false)}
                    onSuccess={() => onClose()}
                    description="If you leave this page, all the information you've entered will be lost. Are you sure you want to continue?"
                />
            )}

            {showConfirmationModal && (
                <EstimateConfirmationModal
                    show={showConfirmationModal}
                    onClose={() => setShowConfirmationModal(false)}
                    onNo={handleConfirmationNo}
                    onYes={handleConfirmationYes}
                />
            )}

            {showEstimateSummary && (
                <EstimateSummaryModal
                    show={showEstimateSummary}
                    onClose={() => setShowEstimateSummary(false)}
                    onGoBack={() => setShowEstimateSummary(false)}
                    onSubmitForApproval={() => {
                        setShowEstimateSummary(false);
                        onSubmit(formValues);
                    }}
                    items={currentEstimateItems}
                />
            )}
        </Modal>
    );
};

export default CreateEstimateModal;
